"""
Template Advanced Search Service

Implements comprehensive search capabilities for Template data with multiple strategies.
Supports searching across documents, users, projects, and other entities.

CUSTOMIZATION INSTRUCTIONS:
1. Replace "Template" with your connector name throughout this file
2. Update the entity type mappings to match your schema
3. Update the relationship mappings for graph traversal
4. Implement your specific search strategies
5. Update the search result processing logic
"""

import logging
import asyncio
import hashlib
import json
from typing import List, Dict, Any, Optional, Set, Tuple
from datetime import datetime, timedelta
from enum import Enum

from app.services.neo4j_service import execute_read_query, execute_write_query
from app.utils.redis.redis_service import RedisService
from app.utils.pinecone.pinecone_service import PineconeService

logger = logging.getLogger(__name__)


class TemplateSearchType(Enum):
    """Template entity search types - CUSTOMIZE for your entities"""
    DOCUMENT = "document"
    USER = "user"
    PROJECT = "project"
    ALL = "all"


class SearchStrategy(Enum):
    """Search strategy types"""
    GRAPH_TRAVERSAL = "graph_traversal"
    SEMANTIC_ONLY = "semantic_only"
    ENTITY_CENTRIC = "entity_centric"
    RELATIONSHIP_CENTRIC = "relationship_centric"
    HYBRID = "hybrid"


class TemplateSearchService:
    """Advanced Template search service with multiple strategies"""
    
    def __init__(self, pinecone_service: PineconeService = None, redis_service: RedisService = None):
        self.pinecone_service = pinecone_service
        self.redis_service = redis_service
        self.cache_ttl = 300  # 5 minutes
        
        # Template entity type mappings - CUSTOMIZE for your entities
        self.entity_types = {
            'document': 'TemplateDocument',
            'user': 'TemplateUser',
            'project': 'TemplateProject',
        }
        
        # Relationship mappings for graph traversal - CUSTOMIZE for your relationships
        self.relationship_types = {
            'creates': 'CREATES',
            'member_of': 'MEMBER_OF',
            'belongs_to': 'BELONGS_TO',
            'references': 'REFERENCES',
            'collaborates_on': 'COLLABORATES_ON',
            'similar_to': 'SIMILAR_TO',
            'related_to': 'RELATED_TO'
        }

    async def search(self, query: str, organisation_id: str, user_id: str = None,
                    search_type: TemplateSearchType = TemplateSearchType.ALL, 
                    strategy: SearchStrategy = SearchStrategy.HYBRID, 
                    limit: int = 20, filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Main search entry point with intelligent strategy selection
        
        CUSTOMIZE: Update the search logic for your specific needs
        
        Args:
            query: Search query string
            organisation_id: Organisation ID
            user_id: Optional user ID for user-specific searches
            search_type: Type of Template entity to search
            strategy: Search strategy to use
            limit: Maximum results to return
            filters: Additional search filters
            
        Returns:
            Dict containing search results and metadata
        """
        try:
            logger.debug(f"Template search: query='{query}', type={search_type.value}, strategy={strategy.value}")
            
            # Check cache first
            cache_key = self._generate_cache_key(query, organisation_id, search_type, strategy, filters)
            cached_result = self._get_cached_result(cache_key)
            if cached_result:
                return cached_result
            
            # Execute search based on strategy
            if strategy == SearchStrategy.SEMANTIC_ONLY:
                results = await self._semantic_search(query, organisation_id, search_type, limit, filters)
            elif strategy == SearchStrategy.GRAPH_TRAVERSAL:
                results = await self._graph_traversal_search(query, organisation_id, search_type, limit, filters)
            elif strategy == SearchStrategy.ENTITY_CENTRIC:
                results = await self._entity_centric_search(query, organisation_id, search_type, limit, filters)
            elif strategy == SearchStrategy.RELATIONSHIP_CENTRIC:
                results = await self._relationship_centric_search(query, organisation_id, search_type, limit, filters)
            else:  # HYBRID
                results = await self._hybrid_search(query, organisation_id, search_type, limit, filters)
            
            # Cache results
            self._cache_result(cache_key, results)
            
            return results
            
        except Exception as e:
            logger.error(f"Template search failed: {str(e)}")
            return {
                'results': [],
                'total_count': 0,
                'search_time_ms': 0,
                'strategy_used': strategy.value,
                'error': str(e)
            }

    async def _semantic_search(self, query: str, organisation_id: str, 
                              search_type: TemplateSearchType, limit: int, 
                              filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Perform semantic search using Pinecone embeddings
        
        CUSTOMIZE: Update for your embedding metadata structure
        """
        try:
            start_time = datetime.now()
            
            if not self.pinecone_service:
                raise Exception("Pinecone service not available")
            
            # Prepare search filters
            search_filters = {'organisation_id': organisation_id}
            
            if search_type != TemplateSearchType.ALL:
                search_filters['entity_type'] = self.entity_types.get(search_type.value)
            
            if filters:
                search_filters.update(filters)
            
            # Perform semantic search
            success, message, search_results = self.pinecone_service.search_vectors(
                query_text=query,
                organisation_id=organisation_id,
                top_k=limit,
                filters=search_filters
            )
            
            if not success:
                raise Exception(f"Pinecone search failed: {message}")
            
            # Process results
            results = []
            for match in search_results.matches:
                metadata = match.metadata
                results.append({
                    'id': metadata.get('id'),
                    'title': metadata.get('title', ''),
                    'content': metadata.get('content', ''),
                    'entity_type': metadata.get('entity_type'),
                    'score': float(match.score),
                    'metadata': metadata,
                    'search_method': 'semantic'
                })
            
            search_time_ms = (datetime.now() - start_time).total_seconds() * 1000
            
            return {
                'results': results,
                'total_count': len(results),
                'search_time_ms': search_time_ms,
                'strategy_used': 'semantic_only'
            }
            
        except Exception as e:
            logger.error(f"Semantic search failed: {str(e)}")
            return {
                'results': [],
                'total_count': 0,
                'search_time_ms': 0,
                'strategy_used': 'semantic_only',
                'error': str(e)
            }

    async def _graph_traversal_search(self, query: str, organisation_id: str,
                                     search_type: TemplateSearchType, limit: int,
                                     filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Perform graph traversal search using Neo4j
        
        CUSTOMIZE: Update the graph queries for your specific relationships
        """
        try:
            start_time = datetime.now()
            
            # Build graph traversal query based on search type
            if search_type == TemplateSearchType.DOCUMENT:
                cypher_query = self._build_document_graph_query(query, filters)
            elif search_type == TemplateSearchType.USER:
                cypher_query = self._build_user_graph_query(query, filters)
            elif search_type == TemplateSearchType.PROJECT:
                cypher_query = self._build_project_graph_query(query, filters)
            else:
                cypher_query = self._build_general_graph_query(query, filters)
            
            # Execute query
            params = {
                'organisation_id': organisation_id,
                'query': query,
                'limit': limit
            }
            if filters:
                params.update(filters)
            
            neo4j_results = execute_read_query(cypher_query, params)
            
            # Process results
            results = []
            for record in neo4j_results:
                entity = record.get('entity')
                if entity:
                    results.append({
                        'id': entity.get('id'),
                        'title': entity.get('title') or entity.get('name') or entity.get('display_name', ''),
                        'content': entity.get('content', ''),
                        'entity_type': record.get('entity_type', ''),
                        'score': record.get('score', 1.0),
                        'metadata': dict(entity),
                        'search_method': 'graph_traversal',
                        'path_info': record.get('path_info', {})
                    })
            
            search_time_ms = (datetime.now() - start_time).total_seconds() * 1000
            
            return {
                'results': results,
                'total_count': len(results),
                'search_time_ms': search_time_ms,
                'strategy_used': 'graph_traversal'
            }
            
        except Exception as e:
            logger.error(f"Graph traversal search failed: {str(e)}")
            return {
                'results': [],
                'total_count': 0,
                'search_time_ms': 0,
                'strategy_used': 'graph_traversal',
                'error': str(e)
            }

    async def _hybrid_search(self, query: str, organisation_id: str,
                            search_type: TemplateSearchType, limit: int,
                            filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Perform hybrid search combining semantic and graph traversal
        
        CUSTOMIZE: Adjust the weighting and combination logic for your needs
        """
        try:
            start_time = datetime.now()
            
            # Run both searches concurrently
            semantic_task = self._semantic_search(query, organisation_id, search_type, limit//2, filters)
            graph_task = self._graph_traversal_search(query, organisation_id, search_type, limit//2, filters)
            
            semantic_results, graph_results = await asyncio.gather(semantic_task, graph_task)
            
            # Combine and deduplicate results
            combined_results = []
            seen_ids = set()
            
            # Add semantic results with higher weight for semantic score
            for result in semantic_results.get('results', []):
                if result['id'] not in seen_ids:
                    result['combined_score'] = result['score'] * 0.7  # Weight semantic results
                    combined_results.append(result)
                    seen_ids.add(result['id'])
            
            # Add graph results with weight for graph relevance
            for result in graph_results.get('results', []):
                if result['id'] not in seen_ids:
                    result['combined_score'] = result['score'] * 0.5  # Weight graph results
                    combined_results.append(result)
                    seen_ids.add(result['id'])
                else:
                    # Boost score for items found in both searches
                    for existing in combined_results:
                        if existing['id'] == result['id']:
                            existing['combined_score'] += result['score'] * 0.3
                            existing['search_method'] = 'hybrid'
                            break
            
            # Sort by combined score
            combined_results.sort(key=lambda x: x.get('combined_score', 0), reverse=True)
            
            # Limit results
            final_results = combined_results[:limit]
            
            search_time_ms = (datetime.now() - start_time).total_seconds() * 1000
            
            return {
                'results': final_results,
                'total_count': len(final_results),
                'search_time_ms': search_time_ms,
                'strategy_used': 'hybrid',
                'semantic_count': len(semantic_results.get('results', [])),
                'graph_count': len(graph_results.get('results', []))
            }
            
        except Exception as e:
            logger.error(f"Hybrid search failed: {str(e)}")
            return {
                'results': [],
                'total_count': 0,
                'search_time_ms': 0,
                'strategy_used': 'hybrid',
                'error': str(e)
            }

    # Helper methods - CUSTOMIZE these for your specific graph queries

    def _build_document_graph_query(self, query: str, filters: Dict[str, Any] = None) -> str:
        """Build graph query for document search - CUSTOMIZE for your schema"""
        return """
        MATCH (d:TemplateDocument {organisation_id: $organisation_id})
        WHERE d.title CONTAINS $query OR d.content CONTAINS $query
        OPTIONAL MATCH (d)<-[:CREATES]-(u:TemplateUser)
        OPTIONAL MATCH (d)-[:BELONGS_TO]->(p:TemplateProject)
        RETURN d as entity, 'TemplateDocument' as entity_type,
               score() as score,
               {author: u.display_name, project: p.name} as path_info
        ORDER BY score DESC
        LIMIT $limit
        """

    def _build_user_graph_query(self, query: str, filters: Dict[str, Any] = None) -> str:
        """Build graph query for user search - CUSTOMIZE for your schema"""
        return """
        MATCH (u:TemplateUser {organisation_id: $organisation_id})
        WHERE u.display_name CONTAINS $query OR u.username CONTAINS $query
        OPTIONAL MATCH (u)-[:CREATES]->(d:TemplateDocument)
        OPTIONAL MATCH (u)-[:MEMBER_OF]->(p:TemplateProject)
        RETURN u as entity, 'TemplateUser' as entity_type,
               score() as score,
               {documents_count: count(DISTINCT d), projects_count: count(DISTINCT p)} as path_info
        ORDER BY score DESC
        LIMIT $limit
        """

    def _build_project_graph_query(self, query: str, filters: Dict[str, Any] = None) -> str:
        """Build graph query for project search - CUSTOMIZE for your schema"""
        return """
        MATCH (p:TemplateProject {organisation_id: $organisation_id})
        WHERE p.name CONTAINS $query OR p.description CONTAINS $query
        OPTIONAL MATCH (p)<-[:BELONGS_TO]-(d:TemplateDocument)
        OPTIONAL MATCH (p)<-[:MEMBER_OF]-(u:TemplateUser)
        RETURN p as entity, 'TemplateProject' as entity_type,
               score() as score,
               {documents_count: count(DISTINCT d), members_count: count(DISTINCT u)} as path_info
        ORDER BY score DESC
        LIMIT $limit
        """

    def _build_general_graph_query(self, query: str, filters: Dict[str, Any] = None) -> str:
        """Build general graph query for all entities - CUSTOMIZE for your schema"""
        return """
        CALL {
            MATCH (d:TemplateDocument {organisation_id: $organisation_id})
            WHERE d.title CONTAINS $query OR d.content CONTAINS $query
            RETURN d as entity, 'TemplateDocument' as entity_type, score() as score, {} as path_info
            UNION
            MATCH (u:TemplateUser {organisation_id: $organisation_id})
            WHERE u.display_name CONTAINS $query OR u.username CONTAINS $query
            RETURN u as entity, 'TemplateUser' as entity_type, score() as score, {} as path_info
            UNION
            MATCH (p:TemplateProject {organisation_id: $organisation_id})
            WHERE p.name CONTAINS $query OR p.description CONTAINS $query
            RETURN p as entity, 'TemplateProject' as entity_type, score() as score, {} as path_info
        }
        RETURN entity, entity_type, score, path_info
        ORDER BY score DESC
        LIMIT $limit
        """

    # Placeholder methods for other search strategies - CUSTOMIZE as needed

    async def _entity_centric_search(self, query: str, organisation_id: str,
                                   search_type: TemplateSearchType, limit: int,
                                   filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """Entity-centric search focusing on specific entity properties"""
        # CUSTOMIZE: Implement entity-centric search logic
        return await self._graph_traversal_search(query, organisation_id, search_type, limit, filters)

    async def _relationship_centric_search(self, query: str, organisation_id: str,
                                         search_type: TemplateSearchType, limit: int,
                                         filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """Relationship-centric search focusing on connections between entities"""
        # CUSTOMIZE: Implement relationship-centric search logic
        return await self._graph_traversal_search(query, organisation_id, search_type, limit, filters)

    # Cache management methods

    def _generate_cache_key(self, query: str, organisation_id: str,
                           search_type: TemplateSearchType, strategy: SearchStrategy,
                           filters: Dict[str, Any] = None) -> str:
        """Generate cache key for search results"""
        key_data = {
            'query': query,
            'organisation_id': organisation_id,
            'search_type': search_type.value,
            'strategy': strategy.value,
            'filters': filters or {}
        }
        key_string = json.dumps(key_data, sort_keys=True)
        return f"template_search:{hashlib.md5(key_string.encode()).hexdigest()}"

    def _get_cached_result(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get cached search result"""
        try:
            if self.redis_service:
                cached_data = self.redis_service.get(cache_key)
                if cached_data:
                    return json.loads(cached_data)
        except Exception as e:
            logger.warning(f"Failed to get cached result: {str(e)}")
        return None

    def _cache_result(self, cache_key: str, result: Dict[str, Any]):
        """Cache search result"""
        try:
            if self.redis_service:
                self.redis_service.setex(
                    cache_key,
                    self.cache_ttl,
                    json.dumps(result, default=str)
                )
        except Exception as e:
            logger.warning(f"Failed to cache result: {str(e)}")
